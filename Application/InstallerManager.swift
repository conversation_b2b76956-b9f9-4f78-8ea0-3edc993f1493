import CoreServices
import Foundation
import InputMethodKit
import AppKit

enum InstallError: Error, LocalizedError {
    case sourceNotFound
    case destinationNotFound
    case copyFailed(Error)
    case registrationFailed(OSStatus)
    case inputSourceNotFound
    case operationFailed(OSStatus)
    case uninstallFailed(Error)
    case unzipFailed(Error)

    var errorDescription: String? {
        switch self {
        case .sourceNotFound:
            return NSLocalizedString(
                "error_source_not_found",
                comment: "Error message when embedded input method file is not found")
        case .destinationNotFound:
            return NSLocalizedString(
                "error_destination_not_found",
                comment: "Error message when system input method directory cannot be located")
        case .copyFailed(let error):
            return String(
                format: NSLocalizedString(
                    "error_copy_failed", comment: "Error message when file copy fails"),
                error.localizedDescription)
        case .registrationFailed(let status):
            return String(
                format: NSLocalizedString(
                    "error_registration_failed",
                    comment: "Error message when input method registration fails"), status)
        case .inputSourceNotFound:
            return NSLocalizedString(
                "error_input_source_not_found",
                comment: "Error message when Siflowtype input method is not found")
        case .operationFailed(let status):
            return String(
                format: NSLocalizedString(
                    "error_operation_failed", comment: "Error message when operation fails"), status
            )
        case .uninstallFailed(let error):
            return String(
                format: NSLocalizedString(
                    "error_uninstall_failed", comment: "Error message when uninstall fails"),
                error.localizedDescription)
        case .unzipFailed(let error):
            return String(
                format: NSLocalizedString(
                    "error_unzip_failed", comment: "Error message when unzip fails"),
                error.localizedDescription)
        }
    }
}

final class InstallerManager {

    private let logger = Logger.shared

    private let inputMethodAppName = "Siflowtype"
    private let inputMethodsDirectory: URL = URL(fileURLWithPath: "/Library/Input Methods")
    private let bundleId = "com.siflowtype.inputmethod.siflowtype"

    // 解压 ZIP 文件的私有方法
    private func unzip(at sourceURL: URL, to destinationURL: URL) throws {
        let task = Process()
        task.launchPath = "/usr/bin/unzip"
        task.arguments = ["-o", sourceURL.path, "-d", destinationURL.path]

        let pipe = Pipe()
        task.standardOutput = pipe
        task.standardError = pipe

        do {
            try task.run()
            task.waitUntilExit()

            if task.terminationStatus != 0 {
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                let output = String(data: data, encoding: .utf8) ?? "Unknown error"
                throw NSError(
                    domain: "UnzipError", code: Int(task.terminationStatus),
                    userInfo: [NSLocalizedDescriptionKey: output])
            }
        } catch {
            throw InstallError.unzipFailed(error)
        }
    }

    // 精确定位 Siflowtype 输入法的私有方法，返回所有匹配的输入源
    private func getSiflowtypeInputSources() -> [TISInputSource] {
        logger.log(level: .info, message: "Searching for Siflowtype input sources with bundle ID: \(bundleId)")

        // 使用可选绑定安全地处理 TISCreateInputSourceList 的返回值，避免强制解包导致的崩溃
        guard
            let sourceList = TISCreateInputSourceList(nil, true)?.takeRetainedValue() as? [TISInputSource]
        else {
            logger.log(level: .error, message: "Failed to get system input source list")
            return []
        }

        logger.log(level: .info, message: "Found \(sourceList.count) total input sources in system")

        var matchingSources: [TISInputSource] = []
        var siflowtypeRelatedSources: [String] = []

        for inputSource in sourceList {
            // 安全地获取输入源 ID
            guard
                let sourceIDPtr = TISGetInputSourceProperty(inputSource, kTISPropertyInputSourceID)
            else {
                continue
            }

            // 将非托管的 CFString 指针安全地转换为 Swift String
            let sourceID =
                Unmanaged<CFString>.fromOpaque(sourceIDPtr).takeUnretainedValue() as String

            // 记录所有包含 "siflowtype" 的输入源（不区分大小写）
            if sourceID.lowercased().contains("siflowtype") {
                siflowtypeRelatedSources.append(sourceID)
            }

            if sourceID == bundleId {
                matchingSources.append(inputSource)
                logger.log(level: .info, message: "Found exact match for bundle ID: \(sourceID)")
            }
        }

        // 记录所有相关的输入源
        if !siflowtypeRelatedSources.isEmpty {
            logger.log(level: .info, message: "Found Siflowtype-related input sources: \(siflowtypeRelatedSources)")
        }

        logger.log(level: .info, message: "Found \(matchingSources.count) matching Siflowtype input sources")

        return matchingSources
    }
    
    // 获取第一个 Siflowtype 输入法（用于启用和选择操作）
    private func getFirstSiflowtypeInputSource() -> TISInputSource? {
        return getSiflowtypeInputSources().first
    }

    // 安装输入法
    func install() throws {

        // 1. 获取内嵌输入法 ZIP 文件的源路径
        guard let zipURL = Bundle.main.url(forResource: inputMethodAppName, withExtension: "zip")
        else {
            throw InstallError.sourceNotFound
        }

        // 2. 获取目标安装目录 (~/Library/Input Methods/)
        let fileManager = FileManager.default
        let destinationDirectoryURL = inputMethodsDirectory

        // 3. 确保目标目录存在
        do {
            try fileManager.createDirectory(
                at: destinationDirectoryURL, withIntermediateDirectories: true, attributes: nil)
        } catch {
            throw InstallError.destinationNotFound
        }

        let destinationURL = destinationDirectoryURL.appendingPathComponent(
            "\(inputMethodAppName).app")

        do {
            // 4. 如果已存在旧版本，先删除
            if fileManager.fileExists(atPath: destinationURL.path) {
                try fileManager.removeItem(at: destinationURL)
            }

            // 5. 解压 ZIP 文件到目标目录
            try unzip(at: zipURL, to: destinationDirectoryURL)

            // 6. 注册输入法
            try register()
            
            // 7. 安装后刷新缓存
            refreshInputSourceCache()

        } catch let error as InstallError {
            logger.log(level: .error, message: "Installation failed: \(error.localizedDescription)")
            throw error
        } catch {
            logger.log(level: .error, message: "An unexpected error occurred during installation: \(error.localizedDescription)")
            throw InstallError.copyFailed(error)
        }
    }
    
    // 安装并启用输入法（一体化操作，避免重复调用）
    func installAndEnable() throws {
        // 1. 先执行安装
        try install()
        
        // 2. 启用输入法
        try enable()
        
        // 3. 选择输入法
        try select()
    }

    // 注册输入法
    func register() throws {
        // 先检查是否已经注册过，避免重复注册
        if !getSiflowtypeInputSources().isEmpty {
            // 输入法已经注册，无需重复注册
            return
        }
        
        let appURL = inputMethodsDirectory.appendingPathComponent("\(inputMethodAppName).app")

        let cfURL = appURL as CFURL
        let status = TISRegisterInputSource(cfURL)

        if status != noErr {
            throw InstallError.registrationFailed(status)
        }
    }

    // 启用输入法
    func enable() throws {
        logger.log(level: .info, message: "Starting input method enable process")

        // 1. 检查是否能找到输入源
        guard let inputSource = getFirstSiflowtypeInputSource() else {
            logger.log(level: .error, message: "Failed to find Siflowtype input source during enable operation")
            throw InstallError.inputSourceNotFound
        }

        logger.log(level: .info, message: "Found Siflowtype input source, analyzing properties...")

        // 2. 记录输入源的详细属性信息
        logInputSourceProperties(inputSource)

        // 3. 检查当前启用状态
        let enabled = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnabled)
        let selectable = getBool(for: inputSource, key: kTISPropertyInputSourceIsSelectCapable)
        let enableCapable = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnableCapable)

        logger.log(level: .info, message: "Input source status - Enabled: \(enabled), Selectable: \(selectable), EnableCapable: \(enableCapable)")

        if enabled {
            logger.log(level: .info, message: "Input method is already enabled, no action needed")
            return
        }

        if !enableCapable {
            logger.log(level: .warning, message: "Input source is not enable-capable, this may indicate configuration issues")
        }

        // 4. 尝试启用输入法
        logger.log(level: .info, message: "Attempting to enable input method...")
        let status = TISEnableInputSource(inputSource)

        if status != noErr {
            logger.log(level: .error, message: "Failed to enable input method, OSStatus: \(status)")
            throw InstallError.operationFailed(status)
        }

        logger.log(level: .info, message: "Input method enabled successfully, refreshing cache...")

        // 5. 启用后刷新缓存，避免重复显示
        refreshInputSourceCache()

        // 6. 验证启用结果
        let newEnabled = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnabled)
        logger.log(level: .info, message: "Enable operation completed, final enabled status: \(newEnabled)")
    }

    // 禁用输入法
    func disable() throws {
        let inputSources = getSiflowtypeInputSources()
        guard !inputSources.isEmpty else {
            throw InstallError.inputSourceNotFound
        }

        // 禁用所有匹配的输入法实例
        for inputSource in inputSources {
            let enabled = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnabled)
            if enabled {
                let status = TISDisableInputSource(inputSource)
                if status != noErr {
                    throw InstallError.operationFailed(status)
                }
            }
        }
        
        // 禁用后刷新缓存，确保系统及时更新
        refreshInputSourceCache()
    }

    // 切换到该输入法
    func select() throws {
        guard let inputSource = getFirstSiflowtypeInputSource() else {
            throw InstallError.inputSourceNotFound
        }

        let enabled = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnabled)
        let selectable = getBool(for: inputSource, key: kTISPropertyInputSourceIsSelectCapable)
        let selected = getBool(for: inputSource, key: kTISPropertyInputSourceIsSelected)

        if enabled && selectable && !selected {
            let status = TISSelectInputSource(inputSource)
            if status != noErr {
                throw InstallError.operationFailed(status)
            }
        }
    }

    // 检查输入法当前是否已启用
    func isEnabled() -> Bool {
        let inputSources = getSiflowtypeInputSources()
        
        // 只要有一个实例启用就认为输入法已启用
        for inputSource in inputSources {
            if getBool(for: inputSource, key: kTISPropertyInputSourceIsEnabled) {
                return true
            }
        }
        
        return false
    }

    // 卸载输入法
    func uninstall() throws {
        // 1. 禁用并移除所有输入法实例
        try removeAllSiflowtypeInputSources()

        // 2. 获取输入法目标路径
        let fileManager = FileManager.default
        let appURL = inputMethodsDirectory.appendingPathComponent("\(inputMethodAppName).app")

        // 3. 检查文件是否存在
        guard fileManager.fileExists(atPath: appURL.path) else {
            // 如果文件不存在，认为已经卸载完成
            return
        }

        do {
            // 4. 删除输入法文件
            try fileManager.removeItem(at: appURL)
            
            // 5. 刷新输入法缓存，确保系统重新扫描输入法列表
            refreshInputSourceCache()
            
            // 6. 关闭系统设置应用，避免输入法缓存未刷新问题
            closeSystemSettings()
        } catch {
            logger.log(level: .error, message: "An unexpected error occurred during uninstallation: \(error.localizedDescription)")
            throw InstallError.uninstallFailed(error)
        }
    }
    
    // 禁用所有重复的 Siflowtype 输入法实例
    private func removeAllSiflowtypeInputSources() throws {
        let inputSources = getSiflowtypeInputSources()
        
        for inputSource in inputSources {
            // 禁用所有已启用的输入法实例
            let enabled = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnabled)
            if enabled {
                let disableStatus = TISDisableInputSource(inputSource)
                if disableStatus != noErr {
                    print("Warning: Failed to disable input source, status: \(disableStatus)")
                }
            }
        }
    }

    // 检查输入法文件是否已存在于目标位置
    func isInstalled() -> Bool {
        let fileManager = FileManager.default
        let appURL = inputMethodsDirectory.appendingPathComponent("\(inputMethodAppName).app")
        return fileManager.fileExists(atPath: appURL.path)
    }

    // 打开系统设置中的"输入源"面板
    func openInputSourceSettings() {
        let url = URL(
            string:
                "x-apple.systempreferences:com.apple.Keyboard-Settings.extension?path=InputSources")!
        NSWorkspace.shared.open(url)
    }
    
    // 关闭系统设置应用
    private func closeSystemSettings() {
        let runningApps = NSWorkspace.shared.runningApplications
        
        // 查找并关闭系统设置应用
        for app in runningApps {
            if app.bundleIdentifier == "com.apple.SystemPreferences" || 
               app.bundleIdentifier == "com.apple.Settings" {
                app.terminate()
                break
            }
        }
    }

    // 刷新输入法缓存，强制系统重新扫描输入法列表
    private func refreshInputSourceCache() {
        // 通过切换到系统默认输入法再切换回来的方式刷新缓存
        let sourceList = TISCreateInputSourceList(nil, true)?.takeRetainedValue() as? [TISInputSource]
        
        // 查找系统默认的英文键盘输入法
        if let sourceList = sourceList {
            for inputSource in sourceList {
                if let sourceIDPtr = TISGetInputSourceProperty(inputSource, kTISPropertyInputSourceID) {
                    let sourceID = Unmanaged<CFString>.fromOpaque(sourceIDPtr).takeUnretainedValue() as String
                    
                    // 切换到系统默认输入法（通常是 com.apple.keylayout.ABC）
                    if sourceID == "com.apple.keylayout.ABC" {
                        TISSelectInputSource(inputSource)
                        break
                    }
                }
            }
        }
        
        // 延迟一小段时间让系统处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 这里可以添加额外的刷新逻辑
        }
    }
    
    // 获取布尔属性的辅助方法
    private func getBool(for inputSource: TISInputSource, key: CFString) -> Bool {
        let enabledRef = TISGetInputSourceProperty(inputSource, key)
        guard enabledRef != nil else { return false }
        let enabled = unsafeBitCast(enabledRef, to: CFBoolean.self)
        return CFBooleanGetValue(enabled)
    }

    // 获取字符串属性的辅助方法
    private func getString(for inputSource: TISInputSource, key: CFString) -> String? {
        guard let propertyPtr = TISGetInputSourceProperty(inputSource, key) else {
            return nil
        }
        let cfString = Unmanaged<CFString>.fromOpaque(propertyPtr).takeUnretainedValue()
        return cfString as String
    }

    // 记录输入源的详细属性信息
    private func logInputSourceProperties(_ inputSource: TISInputSource) {
        logger.log(level: .info, message: "=== Input Source Properties Analysis ===")

        // 基本标识信息
        if let sourceID = getString(for: inputSource, key: kTISPropertyInputSourceID) {
            logger.log(level: .info, message: "TISInputSourceID: \(sourceID)")

            // 检查是否匹配预期的 bundle ID
            if sourceID == bundleId {
                logger.log(level: .info, message: "✓ Input source ID matches expected bundle ID")
            } else {
                logger.log(level: .warning, message: "⚠ Input source ID does not match expected bundle ID: \(bundleId)")
            }
        } else {
            logger.log(level: .error, message: "✗ Failed to get TISInputSourceID")
        }

        // 语言设置
        if let language = getString(for: inputSource, key: kTISPropertyIntendedLanguage) {
            logger.log(level: .info, message: "TISIntendedLanguage: \(language)")
        } else {
            logger.log(level: .warning, message: "TISIntendedLanguage: not available")
        }

        // 本地化名称
        if let localizedName = getString(for: inputSource, key: kTISPropertyLocalizedName) {
            logger.log(level: .info, message: "LocalizedName: \(localizedName)")
        }

        // 输入模式类别
        if let category = getString(for: inputSource, key: kTISPropertyInputSourceCategory) {
            logger.log(level: .info, message: "InputSourceCategory: \(category)")
        }

        // 输入模式类型
        if let type = getString(for: inputSource, key: kTISPropertyInputSourceType) {
            logger.log(level: .info, message: "InputSourceType: \(type)")
        }

        // 状态属性
        let enabled = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnabled)
        let selectable = getBool(for: inputSource, key: kTISPropertyInputSourceIsSelectCapable)
        let enableCapable = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnableCapable)
        let selected = getBool(for: inputSource, key: kTISPropertyInputSourceIsSelected)

        logger.log(level: .info, message: "Status Properties:")
        logger.log(level: .info, message: "  - IsEnabled: \(enabled)")
        logger.log(level: .info, message: "  - IsSelectCapable: \(selectable)")
        logger.log(level: .info, message: "  - IsEnableCapable: \(enableCapable)")
        logger.log(level: .info, message: "  - IsSelected: \(selected)")

        // 检查 ComponentInputModeDict 相关配置
        logger.log(level: .info, message: "=== ComponentInputModeDict Validation ===")

        // 验证关键配置项是否符合预期
        validateInputModeConfiguration(inputSource)

        logger.log(level: .info, message: "=== End of Properties Analysis ===")
    }

    // 验证输入模式配置
    private func validateInputModeConfiguration(_ inputSource: TISInputSource) {
        // 检查输入源 ID 是否符合预期格式
        if let sourceID = getString(for: inputSource, key: kTISPropertyInputSourceID) {
            // 检查是否匹配当前配置的 bundle ID
            if sourceID == bundleId {
                logger.log(level: .info, message: "✓ TISInputSourceID matches configured bundle ID: \(bundleId)")
            } else {
                // 也检查是否是预期的完整格式（包含输入模式）
                let expectedFullSourceID = "com.siflowtype.inputmethod.Siflowtype.English"
                if sourceID == expectedFullSourceID {
                    logger.log(level: .info, message: "✓ TISInputSourceID matches expected full format: \(expectedFullSourceID)")
                } else {
                    logger.log(level: .warning, message: "⚠ TISInputSourceID mismatch - Expected: \(bundleId) or \(expectedFullSourceID), Actual: \(sourceID)")
                }
            }
        }

        // 检查语言设置
        if let language = getString(for: inputSource, key: kTISPropertyIntendedLanguage) {
            if language == "en-US" || language == "en" {
                logger.log(level: .info, message: "✓ TISIntendedLanguage is correctly set to English")
            } else {
                logger.log(level: .info, message: "ℹ TISIntendedLanguage is set to: \(language)")
            }
        }

        // 检查输入源类型
        if let type = getString(for: inputSource, key: kTISPropertyInputSourceType) {
            if type == kTISTypeKeyboardInputMethodWithoutModes as String {
                logger.log(level: .info, message: "✓ Input source type is correctly set for input method")
            } else {
                logger.log(level: .info, message: "ℹ Input source type: \(type)")
            }
        }

        // 检查关键能力
        let enableCapable = getBool(for: inputSource, key: kTISPropertyInputSourceIsEnableCapable)
        let selectCapable = getBool(for: inputSource, key: kTISPropertyInputSourceIsSelectCapable)

        if enableCapable && selectCapable {
            logger.log(level: .info, message: "✓ Input source has required capabilities (enable & select)")
        } else {
            logger.log(level: .warning, message: "⚠ Input source missing capabilities - EnableCapable: \(enableCapable), SelectCapable: \(selectCapable)")
        }
    }
}
