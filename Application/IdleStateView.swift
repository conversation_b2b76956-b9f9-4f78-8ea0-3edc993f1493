import SwiftUI

struct IdleStateView: View {
    @ObservedObject var viewModel: ContentViewModel

    var body: some View {
        VStack(spacing: 24) {
            // 主按钮
            Text(viewModel.mainButtonText)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity, minHeight: 50)
                .background(viewModel.isMainButtonPressed ? Color.gray : Color.black)
                .cornerRadius(12)
                .scaleEffect(viewModel.isMainButtonPressed ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: viewModel.isMainButtonPressed)
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        viewModel.isMainButtonPressed = true
                    }
                    Task {
                        try? await Task.sleep(for: .milliseconds(100))
                        withAnimation(.easeInOut(duration: 0.1)) {
                            viewModel.isMainButtonPressed = false
                        }
                        await viewModel.handleMainAction()
                    }
                }
                .padding(.horizontal, 40)

            // 描述文本
            Text(viewModel.descriptionText)
                .font(.system(size: 14, weight: .regular))
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 20)
        }
        .padding(.bottom, 20)
    }
}
