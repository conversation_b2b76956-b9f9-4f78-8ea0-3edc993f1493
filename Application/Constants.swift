import Foundation

struct Constants {
    static let buttonInstall = NSLocalizedString("button_install", comment: "Install button")
    static let buttonEnable = NSLocalizedString("button_enable", comment: "Enable button")
    static let buttonUninstall = NSLocalizedString("button_uninstall", comment: "Uninstall button")
    static let descriptionNotInstalled = NSLocalizedString("description_not_installed", comment: "Description for not installed state")
    static let descriptionInstalledNotEnabled = NSLocalizedString("description_installed_not_enabled", comment: "Description for installed but not enabled state")
    static let descriptionEnabled = NSLocalizedString("description_enabled", comment: "Description for enabled state")
    static let systemVersionError = NSLocalizedString("system_version_error", comment: "System version error message")
    static let unknownError = NSLocalizedString("unknown_error", comment: "Unknown error message")
    static let appTitle = NSLocalizedString("app_title", comment: "App title")
    static let appSlogan = NSLocalizedString("app_slogan", comment: "App slogan")
    static let appVersion = NSLocalizedString("app_version", comment: "App version")
    static let installing = NSLocalizedString("installing", comment: "Installing message")
    static let installSuccess = NSLocalizedString("install_success", comment: "Install success message")
    static let installSuccessDescription = NSLocalizedString("install_success_description", comment: "Install success description")
    static let buttonDone = NSLocalizedString("button_done", comment: "Done button")
    static let installFailed = NSLocalizedString("install_failed", comment: "Install failed message")
    static let buttonRetry = NSLocalizedString("button_retry", comment: "Retry button")
    static let uninstallSuccess = NSLocalizedString("uninstall_success", comment: "Uninstall success message")
    static let uninstallSuccessDescription = NSLocalizedString("uninstall_success_description", comment: "Uninstall success description")
}