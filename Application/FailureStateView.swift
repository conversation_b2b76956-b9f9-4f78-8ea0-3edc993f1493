import SwiftUI
// 移除 import Constants

struct FailureStateView: View {
    @ObservedObject var viewModel: ContentViewModel

    var body: some View {
        Text(Constants.installFailed)
            .font(.headline)
            .foregroundColor(.red)
        if let message = viewModel.errorMessage {
            Text(message)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal)
        }
        Text(Constants.buttonRetry)
            .font(.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity, minHeight: 44)
            .background(viewModel.isRetryButtonPressed ? Color.gray : Color.black)
            .cornerRadius(10)
            .scaleEffect(viewModel.isRetryButtonPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: viewModel.isRetryButtonPressed)
            .onTapGesture {
                withAnimation(.easeInOut(duration: 0.1)) {
                    viewModel.isRetryButtonPressed = true
                }
                Task {
                    try? await Task.sleep(for: .milliseconds(100))
                    withAnimation(.easeInOut(duration: 0.1)) {
                        viewModel.isRetryButtonPressed = false
                    }
                    viewModel.installationState = .idle
                }
            }
    }
}
