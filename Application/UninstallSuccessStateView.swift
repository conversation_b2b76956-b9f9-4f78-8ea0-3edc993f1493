import SwiftUI

struct UninstallSuccessStateView: View {
    @ObservedObject var viewModel: ContentViewModel
    
    var body: some View {
        VStack(spacing: 24) {
            // 成功图标
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
                .scaleEffect(1.2)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: true)
            
            // 卸载成功标题
            Text(NSLocalizedString("uninstall_success", comment: "卸载成功"))
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.primary)
            
            // 卸载成功描述
            Text(NSLocalizedString("uninstall_success_description", comment: "Siflowtype 输入法已成功从您的系统中移除。"))
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
            
            // 自动关闭提示
            Text(NSLocalizedString("auto_close_message", comment: "应用将在 2 秒后自动关闭..."))
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.secondary)
                .opacity(0.8)
        }
        .padding(.bottom, 20)
    }
}

#Preview {
    UninstallSuccessStateView(viewModel: ContentViewModel())
}