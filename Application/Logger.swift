// File: SiflowtypeInstller/Application/Logger.swift

import Foundation

/// A simple singleton logger to write log messages to a file.
class Logger {

    static let shared = Logger()

    enum LogLevel: String {
        case info = "INFO"
        case warning = "WARNING"
        case error = "ERROR"
    }

    private var logFile: URL?
    private let logQueue = DispatchQueue(label: "com.siflowtype.logger", qos: .utility)
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        return formatter
    }()

    private init() {
        setupLogFile()
    }

    private func setupLogFile() {
        guard let logsDirectory = FileManager.default.urls(for: .libraryDirectory, in: .userDomainMask).first?.appendingPathComponent("Logs") else {
            print("Could not find Library/Logs directory.")
            return
        }

        var isDirectory: ObjCBool = true
        if !FileManager.default.fileExists(atPath: logsDirectory.path, isDirectory: &isDirectory) {
            do {
                try FileManager.default.createDirectory(at: logsDirectory, withIntermediateDirectories: true, attributes: nil)
            } catch {
                print("Error creating Logs directory: \(error.localizedDescription)")
                return
            }
        }

        self.logFile = logsDirectory.appendingPathComponent("siflowtype.log")
    }

    /// Logs a message to the file.
    ///
    /// - Parameters:
    ///   - level: The severity level of the log message.
    ///   - message: The message to log.
    ///   - file: The file where the log was triggered (defaults to the caller's file).
    ///   - function: The function where the log was triggered (defaults to the caller's function).
    ///   - line: The line number where the log was triggered (defaults to the caller's line).
    func log(level: LogLevel, message: String, file: String = #file, function: String = #function, line: Int = #line) {
        logQueue.async { [weak self] in
            self?.writeLog(level: level, message: message, file: file, function: function, line: line)
        }
    }
    
    private func writeLog(level: LogLevel, message: String, file: String, function: String, line: Int) {
        guard let logFile = logFile else {
            print("Log file is not available.")
            return
        }

        let timestamp = dateFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] [\(level.rawValue)] [\((file as NSString).lastPathComponent):\(line) \(function)] - \(message)\n"

        guard let data = logMessage.data(using: .utf8) else {
            print("Failed to encode log message")
            return
        }

        do {
            if FileManager.default.fileExists(atPath: logFile.path) {
                let fileHandle = try FileHandle(forWritingTo: logFile)
                defer { fileHandle.closeFile() }
                fileHandle.seekToEndOfFile()
                fileHandle.write(data)
            } else {
                try data.write(to: logFile, options: .atomic)
            }
        } catch {
            print("Error writing to log file: \(error.localizedDescription)")
        }
    }
}