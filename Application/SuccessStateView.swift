import SwiftUI

struct SuccessStateView: View {
    @ObservedObject var viewModel: ContentViewModel

    var body: some View {
        VStack(spacing: 24) {
            // 成功标题
            Text(NSLocalizedString("install_success", comment: "安装成功"))
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.green)
            
            // 成功描述
            Text(NSLocalizedString("install_success_description", comment: "请在系统设置中启用 Siflowtype 输入法即可开始使用。"))
                .font(.system(size: 14, weight: .regular))
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 20)
            
            // 完成按钮
            Text(NSLocalizedString("button_done", comment: "完成"))
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity, minHeight: 50)
                .background(viewModel.isDoneButtonPressed ? Color.gray : Color.accentColor)
                .cornerRadius(12)
                .scaleEffect(viewModel.isDoneButtonPressed ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: viewModel.isDoneButtonPressed)
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        viewModel.isDoneButtonPressed = true
                    }
                    Task {
                        try? await Task.sleep(for: .milliseconds(100))
                        withAnimation(.easeInOut(duration: 0.1)) {
                            viewModel.isDoneButtonPressed = false
                        }
                        NSApplication.shared.terminate(nil)
                    }
                }
                .padding(.horizontal, 40)
        }
        .padding(.bottom, 20)
    }
}

#Preview {
    SuccessStateView(viewModel: ContentViewModel())
}
