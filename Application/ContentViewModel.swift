import Foundation
import SwiftUI
import AppKit

@MainActor
class ContentViewModel: ObservableObject {

    private let logger = Logger.shared
    @Published var installationState: InstallationState = .idle
    @Published var errorMessage: String?
    @Published var currentStatus: InputMethodStatus = .notInstalled
    @Published var isMainButtonPressed: Bool = false
    @Published var isDoneButtonPressed: Bool = false
    @Published var isRetryButtonPressed: Bool = false

    private let installer = InstallerManager()

    init() {
        updateStatus()
    }

    var mainButtonText: String {
        switch currentStatus {
        case .notInstalled:
            return Constants.buttonInstall
        case .installedButNotEnabled:
            return Constants.buttonEnable
        case .enabled:
            return Constants.buttonUninstall
        }
    }

    var descriptionText: String {
        switch currentStatus {
        case .notInstalled:
            return Constants.descriptionNotInstalled
        case .installedButNotEnabled:
            return Constants.descriptionInstalledNotEnabled
        case .enabled:
            return Constants.descriptionEnabled
        }
    }

    func updateStatus() {
        if installer.isInstalled() {
            if installer.isEnabled() {
                currentStatus = .enabled
            } else {
                currentStatus = .installedButNotEnabled
            }
        } else {
            currentStatus = .notInstalled
        }
    }

    func handleMainAction() async {
        if !isSystemVersionSupported() {
            errorMessage = Constants.systemVersionError
            installationState = .failure
            return
        }

        installationState = .inProgress

        do {
            switch currentStatus {
            case .notInstalled:
                // 安装时自动启用和选择，避免重复操作
                try installer.installAndEnable()
                installationState = .success
            case .installedButNotEnabled:
                try installer.enable()
                try installer.select()
                installationState = .success
            case .enabled:
                try installer.uninstall()
                installationState = .uninstallSuccess
                // 2秒后自动关闭应用
                Task {
                    try? await Task.sleep(for: .seconds(2))
                    NSApplication.shared.terminate(nil)
                }
            }

            updateStatus()
        } catch {
            logger.log(level: .error, message: "Action failed in state \(self.installationState): \(error.localizedDescription)")
            errorMessage = (error as? LocalizedError)?.errorDescription ?? Constants.unknownError
            installationState = .failure
        }
    }

    private func isSystemVersionSupported() -> Bool {
        let version = ProcessInfo.processInfo.operatingSystemVersion
        if version.majorVersion > 11 {
            return true
        } else if version.majorVersion == 11 && version.minorVersion >= 5 {
            return true
        }
        return false
    }
}