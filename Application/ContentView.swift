import Foundation
import SwiftUI
import AppKit

struct ContentView: View {
    @StateObject private var viewModel = ContentViewModel()
    
    var body: some View {
        VStack(spacing: 0) {
            // Logo 区域
            VStack(spacing: 0) {
                Spacer(minLength: 40)
                
                Image(nsImage: NSApp.applicationIconImage)
                    .resizable()
                    .frame(width: 100, height: 100)
                    .cornerRadius(20)
                    .shadow(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
                
                Spacer(minLength: 30)
            }
            
            // 标题区域
            VStack(spacing: 12) {
                Text(Constants.appTitle)
                    .font(.system(size: 28, weight: .bold, design: .default))
                    .foregroundColor(.primary)
                
                Text(Constants.appSlogan)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                
                Text(Constants.appVersion)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.secondary)
                    .opacity(0.6)
            }
            .padding(.bottom, 40)
            
            // 主要内容区域
            switch viewModel.installationState {
            case .idle:
                IdleStateView(viewModel: viewModel)
            case .inProgress:
                InProgressStateView()
            case .success:
                SuccessStateView(viewModel: viewModel)
            case .failure:
                FailureStateView(viewModel: viewModel)
            case .uninstallSuccess:
                UninstallSuccessStateView(viewModel: viewModel)
            }
            
            Spacer()
        }
        .frame(width: 600, height: 520)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

#Preview {
    ContentView()
}
