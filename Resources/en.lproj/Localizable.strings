/* 
  Localizable.strings
  SiflowtypeInstaller

  Created for English localization.
*/

// App Info
"app_title" = "Siflowtype Autocomplete";
"app_slogan" = "You just type, we'll perfect it.";
"app_version" = "0.0.1";

// Status
"status_not_installed" = "Not Installed";
"status_installed_not_enabled" = "Installed but Not Enabled";
"status_enabled" = "Enabled";

// Buttons
"button_install" = "Install";
"button_enable" = "Enable";
"button_uninstall" = "Uninstall";
"button_reinstall" = "Reinstall";
"button_disable" = "Disable";
"button_retry" = "Retry";
"button_done" = "Done";
"button_open_system_settings" = "Open System Settings";

// Messages
"install_description" = "Click the button to install the input method to your Mac.";
"installing" = "Installing...";
"install_success" = "Installation Successful";
"install_success_description" = "Please enable Siflowtype input method in System Settings to start using it.";
"install_failed" = "❌ Installation Failed";
"uninstall_success" = "Uninstall Successful";
"uninstall_success_description" = "Siflowtype input method has been successfully removed from your system.";
"auto_close_message" = "App will close automatically in 2 seconds...";
"unknown_error" = "An unknown error occurred";
"system_version_error" = "Your system version is too low to install this input method. Please upgrade to macOS 11.5 or higher.";
"preview_error_message" = "Installation failed, please check system permission settings.";

// Description Text
"description_not_installed" = "Supports macOS 11.5 and later versions only";
"description_installed_not_enabled" = "Already installed, please click the button to enable";
"description_enabled" = "Already installed, click the button to uninstall";

// Error Messages
"error_source_not_found" = "Embedded input method file not found.";
"error_destination_not_found" = "Cannot locate system input method directory.";
"error_copy_failed" = "File copy failed: %@";
"error_registration_failed" = "Input method registration failed, error code: %d";
"error_input_source_not_found" = "Siflowtype input method not found.";
"error_operation_failed" = "Operation failed, error code: %d";
