# Siflowtype 安装器开发文档

## 1. 项目概述

Siflowtype 安装器是一个为 macOS 设计的 SwiftUI 应用，其核心功能是引导用户完成“Siflowtype”输入法的安装、启用和卸载。应用界面简洁，通过状态驱动，为用户提供清晰的操作指引。

## 2. 架构设计

应用采用经典的 **MVVM (Model-View-ViewModel)** 架构模式，以实现界面、业务逻辑和数据处理的清晰分离。

- **View**: SwiftUI 视图层，负责界面的声明式渲染。它被设计为“哑”组件，仅响应 ViewModel 的状态变化并展示数据，同时将用户交互事件转发给 ViewModel。
- **ViewModel**: `ContentViewModel` 作为视图模型，是 View 和 Model 之间的桥梁。它持有应用的所有状态（如安装状态、错误信息），处理用户交互，并调用 Model 层执行核心业务逻辑。
- **Model**: `InstallerManager` 作为模型层，封装了与输入法交互的所有底层细节，包括文件的复制、解压、通过 `InputMethodKit` 框架注册、启用、禁用和卸载输入法等。

这种分层确保了代码的高内聚和低耦合，使得各部分职责单一，易于测试和维护。

## 3. 核心组件分析

### 3.1. 视图 (Views)

所有视图都位于 `Application/` 目录下，并基于当前安装状态动态切换。

- **`ContentView.swift`**: 应用的主视图，作为容器承载所有子视图。它持有一个 `@StateObject` 修饰的 `ContentViewModel` 实例，并使用 `switch` 语句根据 `viewModel.installationState` 的值来动态展示不同的状态视图。

- **`IdleStateView.swift`**: 初始状态视图。展示“安装”、“启用”或“卸载”按钮，以及对应的描述文本。用户的主要交互从此开始。

- **`InProgressStateView.swift`**: 操作进行中视图。显示一个加载指示器和“正在安装...”之类的文本，为用户提供即时反馈。

- **`SuccessStateView.swift`**: 安装成功视图。告知用户安装已成功，并提供“完成”按钮以退出应用。

- **`FailureStateView.swift`**: 操作失败视图。显示失败信息和具体的错误原因，并提供“重试”按钮，允许用户返回初始状态。

- **`UninstallSuccessStateView.swift`**: 卸载成功视图。告知用户输入法已移除，并提示应用将自动关闭。

### 3.2. 视图模型 (ViewModel)

- **`ContentViewModel.swift`**: 
  - **职责**: 管理应用的整个生命周期和用户交互逻辑。
  - **核心属性**:
    - `@Published var installationState: InstallationState`: 驱动 UI 状态切换的核心。其值的改变会直接导致 `ContentView` 渲染不同的子视图。
    - `@Published var currentStatus: InputMethodStatus`: 表示输入法本身的安装状态（未安装、已安装但未启用、已启用），用于决定 `IdleStateView` 中按钮的文本和功能。
    - `@Published var errorMessage: String?`: 存储操作失败时的错误信息。
  - **核心方法**:
    - `init()`: 初始化时调用 `updateStatus()` 来确定输入法的当前状态。
    - `updateStatus()`: 调用 `InstallerManager` 的方法检查输入法是否已安装和启用，并更新 `currentStatus`。
    - `handleMainAction()`: 核心的异步操作处理函数。根据 `currentStatus` 执行安装、启用或卸载逻辑，并在操作过程中更新 `installationState` 以驱动 UI 变化。它还包含了版本检查和完善的 `try-catch` 错误处理。在 `catch` 块中捕获到错误时，会调用日志服务记录详细信息，并将错误呈现给用户。

### 3.3. 模型 (Model)

- **`InstallerManager.swift`**: 
  - **职责**: 封装所有与 macOS 输入法服务交互的复杂操作，为 ViewModel 提供一个简洁、稳定的接口。
  - **核心功能**:
    - **文件操作**: 管理输入法 `.app` 文件（从内嵌的 `.zip` 中解压）到 `/Library/Input Methods` 目录的复制和删除。
    - **输入法管理 (InputMethodKit)**:
      - `register()`: 使用 `TISRegisterInputSource` 注册输入法。
      - `enable()` / `disable()`: 使用 `TISEnableInputSource` 和 `TISDisableInputSource` 启用/禁用输入法。
      - `select()`: 使用 `TISSelectInputSource` 将输入法切换为当前输入源。
      - `isInstalled()` / `isEnabled()`: 通过 `TISCreateInputSourceList` 遍历系统输入源列表，检查 Siflowtype 输入法的存在与状态。
    - **错误处理与日志记录**: 定义了 `InstallError` 枚举，为不同失败场景提供具体的、本地化的错误描述。在错误发生时，`InstallerManager` 会将包含上下文的详细错误信息记录到日志文件中，便于问题排查。
    - **一体化操作**: 提供了 `installAndEnable()` 这样的便捷方法，将安装、启用、选择等多个步骤合并为一个原子操作，简化了 ViewModel 的调用。

### 3.4. 状态与枚举 (Enums)

- **`Enums.swift`**:
  - `InstallationState`: 定义了 UI 的几种可能状态（`idle`, `inProgress`, `success`, `failure`, `uninstallSuccess`），是状态驱动设计的核心。
  - `InputMethodStatus`: 定义了输入法本身的逻辑状态（`notInstalled`, `installedButNotEnabled`, `enabled`），决定了 `IdleStateView` 的具体行为。

### 3.5. 常量 (Constants)

- **`Constants.swift`**: 使用一个 `struct` 统一管理应用中所有的本地化字符串键。所有面向用户的文本都通过 `NSLocalizedString` 进行包装，便于国际化。这种方式使得文本管理集中化，易于维护。

## 4. 核心流程

### 4.1. 安装流程

1.  **启动**: `ContentView` 初始化 `ContentViewModel`。
2.  **状态检查**: `ContentViewModel` 的 `init` 方法调用 `updateStatus()`，`InstallerManager` 检查后发现输入法未安装，`currentStatus` 被设为 `.notInstalled`。
3.  **UI 显示**: `ContentView` 根据 `installationState` (.`idle`) 和 `currentStatus` 显示 `IdleStateView`，按钮文本为“安装”。
4.  **用户点击**: 用户点击“安装”按钮，触发 `handleMainAction()`。
5.  **处理中**: `installationState` 变为 `.inProgress`，UI 显示 `InProgressStateView`。
6.  **执行安装**: `handleMainAction()` 调用 `installer.installAndEnable()`。
    - `InstallerManager` 解压 `Siflowtype.zip` 到 `/Library/Input Methods`。
    - `InstallerManager` 调用 `TISRegisterInputSource` 注册输入法。
    - `InstallerManager` 调用 `TISEnableInputSource` 和 `TISSelectInputSource` 启用并选择输入法。
7.  **成功**: 操作成功后，`installationState` 变为 `.success`，UI 显示 `SuccessStateView`。
8.  **失败**: 若任一步骤出错，`catch` 块捕获错误，`installationState` 变为 `.failure`，UI 显示 `FailureStateView` 及错误信息。

### 4.2. 卸载流程

1.  **状态检查**: 若输入法已安装并启用，`currentStatus` 为 `.enabled`，`IdleStateView` 显示“卸载”按钮。
2.  **用户点击**: 用户点击“卸载”，触发 `handleMainAction()`。
3.  **执行卸载**: `handleMainAction()` 调用 `installer.uninstall()`。
    - `InstallerManager` 调用 `TISDisableInputSource` 禁用输入法。
    - `InstallerManager` 从 `/Library/Input Methods` 删除 `Siflowtype.app`。
4.  **成功**: `installationState` 变为 `.uninstallSuccess`，UI 显示 `UninstallSuccessStateView`，并启动一个 2 秒后关闭应用的定时器。

## 5. 代码规范与实践

- **SwiftUI 最佳实践**: 视图结构清晰，使用 `@StateObject` 和 `@ObservedObject` 进行状态管理。
- **现代并发**: 大量使用 `async/await` 和 `Task` 来处理异步操作，使代码更易读。
- **强类型错误处理**: 自定义 `InstallError` 提供了明确的错误类型。
- **本地化**: 所有面向用户的字符串都通过 `Constants` 和 `NSLocalizedString` 进行管理。
- **UI 反馈**: 通过 `is...ButtonPressed` 状态和 `scaleEffect` 为按钮点击提供了即时的视觉反馈，提升了用户体验。

## 6. 日志记录设计

为了便于开发者和用户排查安装或卸载过程中遇到的问题，应用将引入一套简单的日志记录机制。

- **日志位置**: 所有日志将被统一追加到 `~/Library/Logs/siflowtype.log` 文件中。应用启动时会自动处理文件和目录的创建。
- **日志触发**:
  - **错误日志**: 当 `InstallerManager` 或 `ContentViewModel` 中的操作（如文件复制、输入法注册）失败并抛出错误时，会在 `catch` 块中记录一条 `ERROR` 级别的日志。
  - **信息日志**: 记录应用启动、成功完成安装/卸载等关键节点，作为 `INFO` 级别的日志。
- **日志格式**: 每条日志将包含时间戳、日志级别（`[INFO]` 或 `[ERROR]`）、以及详细的事件描述。例如：
  ```
  [2025-07-20 14:30:15] [ERROR] Installation failed: The operation couldn’t be completed. (SiflowtypeInstller.InstallError error 1.) - Source file not found at expected path.
  ```
- **实现**: 已创建 `Logger.swift` 文件，实现了一个线程安全的 `Logger` 类，并提供 `shared` 单例。该实现包含以下特性：
  - **线程安全**: 使用专用的 `DispatchQueue` 确保多线程环境下的日志写入安全。
  - **资源管理**: 通过 `defer` 语句确保 `FileHandle` 资源正确关闭，避免内存泄漏。
  - **错误处理**: 移除强制解包，添加编码失败和文件操作的错误处理。
  - **时间戳优化**: 使用 `DateFormatter` 提供标准化的时间戳格式。
  - **调用方式**: 所有需要记录日志的地方通过 `Logger.shared.info("...")`, `Logger.shared.warning("...")`, `Logger.shared.error("...")` 进行调用。