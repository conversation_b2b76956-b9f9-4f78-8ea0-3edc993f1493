// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		A614C6372E3DE72E002B8F70 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = A614C6362E3DE72E002B8F70 /* Logger.swift */; };
		A6434AF82E33863500A0F52B /* Siflowtype.zip in CopyFiles */ = {isa = PBXBuildFile; fileRef = A6434AF72E33863500A0F52B /* Siflowtype.zip */; };
		A6434B002E374CBF00A0F52B /* InProgressStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434AFE2E374CBF00A0F52B /* InProgressStateView.swift */; };
		A6434B012E374CBF00A0F52B /* ContentViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434AFA2E374CBF00A0F52B /* ContentViewModel.swift */; };
		A6434B022E374CBF00A0F52B /* SuccessStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434AFF2E374CBF00A0F52B /* SuccessStateView.swift */; };
		A6434B032E374CBF00A0F52B /* FailureStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434AFC2E374CBF00A0F52B /* FailureStateView.swift */; };
		A6434B042E374CBF00A0F52B /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434AF92E374CBF00A0F52B /* Constants.swift */; };
		A6434B052E374CBF00A0F52B /* Enums.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434AFB2E374CBF00A0F52B /* Enums.swift */; };
		A6434B062E374CBF00A0F52B /* IdleStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434AFD2E374CBF00A0F52B /* IdleStateView.swift */; };
		A6434B092E37504D00A0F52B /* UninstallSuccessStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6434B082E37504D00A0F52B /* UninstallSuccessStateView.swift */; };
		A6D17BC82E28E8FE001EE69D /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6D17BC02E28E8FE001EE69D /* ContentView.swift */; };
		A6D17BC92E28E8FE001EE69D /* InstallerManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6D17BC22E28E8FE001EE69D /* InstallerManager.swift */; };
		A6D17BCA2E28E8FE001EE69D /* SiflowtypeInstllerApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6D17BC52E28E8FE001EE69D /* SiflowtypeInstllerApp.swift */; };
		A6D17BCB2E28E8FE001EE69D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A6D17BBF2E28E8FE001EE69D /* Assets.xcassets */; };
		A6D17BCC2E28E8FE001EE69D /* Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = A6D17BC12E28E8FE001EE69D /* Info.plist */; };
		A6D17BCD2E28E8FE001EE69D /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = A6D17BC32E28E8FE001EE69D /* Localizable.strings */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		A6B636682E28D91F0023DD2A /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				A6434AF82E33863500A0F52B /* Siflowtype.zip in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		A614C6362E3DE72E002B8F70 /* Logger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Logger.swift; sourceTree = "<group>"; };
		A6434AF72E33863500A0F52B /* Siflowtype.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; name = Siflowtype.zip; path = dist/Siflowtype.zip; sourceTree = "<group>"; };
		A6434AF92E374CBF00A0F52B /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		A6434AFA2E374CBF00A0F52B /* ContentViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentViewModel.swift; sourceTree = "<group>"; };
		A6434AFB2E374CBF00A0F52B /* Enums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Enums.swift; sourceTree = "<group>"; };
		A6434AFC2E374CBF00A0F52B /* FailureStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FailureStateView.swift; sourceTree = "<group>"; };
		A6434AFD2E374CBF00A0F52B /* IdleStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IdleStateView.swift; sourceTree = "<group>"; };
		A6434AFE2E374CBF00A0F52B /* InProgressStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InProgressStateView.swift; sourceTree = "<group>"; };
		A6434AFF2E374CBF00A0F52B /* SuccessStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuccessStateView.swift; sourceTree = "<group>"; };
		A6434B082E37504D00A0F52B /* UninstallSuccessStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UninstallSuccessStateView.swift; sourceTree = "<group>"; };
		A6B636472E28D8710023DD2A /* SiflowtypeInstller.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SiflowtypeInstller.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A6D17BBF2E28E8FE001EE69D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A6D17BC02E28E8FE001EE69D /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A6D17BC12E28E8FE001EE69D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A6D17BC22E28E8FE001EE69D /* InstallerManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InstallerManager.swift; sourceTree = "<group>"; };
		A6D17BC42E28E8FE001EE69D /* SiflowtypeInstller.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SiflowtypeInstller.entitlements; sourceTree = "<group>"; };
		A6D17BC52E28E8FE001EE69D /* SiflowtypeInstllerApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SiflowtypeInstllerApp.swift; sourceTree = "<group>"; };
		A6D17BC62E28E8FE001EE69D /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		A6D17BC72E28E8FE001EE69D /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		A6D17BCE2E28E99B001EE69D /* Siflowtype.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; name = Siflowtype.zip; path = ../Siflowtype/dist/Siflowtype.zip; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A6B636442E28D8710023DD2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A6B6363E2E28D8710023DD2A = {
			isa = PBXGroup;
			children = (
				A6434AF72E33863500A0F52B /* Siflowtype.zip */,
				A6D17BD22E28E9B9001EE69D /* Resources */,
				A6D17BD02E28E9A9001EE69D /* Application */,
				A6D17BCE2E28E99B001EE69D /* Siflowtype.zip */,
				A6B636482E28D8710023DD2A /* Products */,
			);
			sourceTree = "<group>";
		};
		A6B636482E28D8710023DD2A /* Products */ = {
			isa = PBXGroup;
			children = (
				A6B636472E28D8710023DD2A /* SiflowtypeInstller.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A6D17BD02E28E9A9001EE69D /* Application */ = {
			isa = PBXGroup;
			children = (
				A614C6362E3DE72E002B8F70 /* Logger.swift */,
				A6434B082E37504D00A0F52B /* UninstallSuccessStateView.swift */,
				A6434AF92E374CBF00A0F52B /* Constants.swift */,
				A6434AFA2E374CBF00A0F52B /* ContentViewModel.swift */,
				A6434AFB2E374CBF00A0F52B /* Enums.swift */,
				A6434AFC2E374CBF00A0F52B /* FailureStateView.swift */,
				A6434AFD2E374CBF00A0F52B /* IdleStateView.swift */,
				A6434AFE2E374CBF00A0F52B /* InProgressStateView.swift */,
				A6434AFF2E374CBF00A0F52B /* SuccessStateView.swift */,
				A6D17BC02E28E8FE001EE69D /* ContentView.swift */,
				A6D17BC22E28E8FE001EE69D /* InstallerManager.swift */,
				A6D17BC52E28E8FE001EE69D /* SiflowtypeInstllerApp.swift */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		A6D17BD22E28E9B9001EE69D /* Resources */ = {
			isa = PBXGroup;
			children = (
				A6D17BC42E28E8FE001EE69D /* SiflowtypeInstller.entitlements */,
				A6D17BC32E28E8FE001EE69D /* Localizable.strings */,
				A6D17BBF2E28E8FE001EE69D /* Assets.xcassets */,
				A6D17BC12E28E8FE001EE69D /* Info.plist */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A6B636462E28D8710023DD2A /* SiflowtypeInstller */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A6B636532E28D8720023DD2A /* Build configuration list for PBXNativeTarget "SiflowtypeInstller" */;
			buildPhases = (
				A6B636432E28D8710023DD2A /* Sources */,
				A6B636442E28D8710023DD2A /* Frameworks */,
				A6B636452E28D8710023DD2A /* Resources */,
				A6B636682E28D91F0023DD2A /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SiflowtypeInstller;
			packageProductDependencies = (
			);
			productName = SiflowtypeInstller;
			productReference = A6B636472E28D8710023DD2A /* SiflowtypeInstller.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A6B6363F2E28D8710023DD2A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					A6B636462E28D8710023DD2A = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = A6B636422E28D8710023DD2A /* Build configuration list for PBXProject "SiflowtypeInstller" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = A6B6363E2E28D8710023DD2A;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = A6B636482E28D8710023DD2A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A6B636462E28D8710023DD2A /* SiflowtypeInstller */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A6B636452E28D8710023DD2A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A6D17BCB2E28E8FE001EE69D /* Assets.xcassets in Resources */,
				A6D17BCC2E28E8FE001EE69D /* Info.plist in Resources */,
				A6D17BCD2E28E8FE001EE69D /* Localizable.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A6B636432E28D8710023DD2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A6434B002E374CBF00A0F52B /* InProgressStateView.swift in Sources */,
				A6434B012E374CBF00A0F52B /* ContentViewModel.swift in Sources */,
				A6434B022E374CBF00A0F52B /* SuccessStateView.swift in Sources */,
				A6434B032E374CBF00A0F52B /* FailureStateView.swift in Sources */,
				A6434B042E374CBF00A0F52B /* Constants.swift in Sources */,
				A6434B052E374CBF00A0F52B /* Enums.swift in Sources */,
				A6434B062E374CBF00A0F52B /* IdleStateView.swift in Sources */,
				A614C6372E3DE72E002B8F70 /* Logger.swift in Sources */,
				A6D17BC82E28E8FE001EE69D /* ContentView.swift in Sources */,
				A6D17BC92E28E8FE001EE69D /* InstallerManager.swift in Sources */,
				A6434B092E37504D00A0F52B /* UninstallSuccessStateView.swift in Sources */,
				A6D17BCA2E28E8FE001EE69D /* SiflowtypeInstllerApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		A6D17BC32E28E8FE001EE69D /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				A6D17BC62E28E8FE001EE69D /* en */,
				A6D17BC72E28E8FE001EE69D /* zh-Hans */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		A6B636512E28D8720023DD2A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A6B636522E28D8720023DD2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		A6B636542E28D8720023DD2A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Resources/SiflowtypeInstller.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Siflowtype Instller";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.siflowtype.SiflowtypeInstller;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		A6B636552E28D8720023DD2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Resources/SiflowtypeInstller.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Siflowtype Instller";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.siflowtype.SiflowtypeInstller;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A6B636422E28D8710023DD2A /* Build configuration list for PBXProject "SiflowtypeInstller" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A6B636512E28D8720023DD2A /* Debug */,
				A6B636522E28D8720023DD2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A6B636532E28D8720023DD2A /* Build configuration list for PBXNativeTarget "SiflowtypeInstller" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A6B636542E28D8720023DD2A /* Debug */,
				A6B636552E28D8720023DD2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A6B6363F2E28D8710023DD2A /* Project object */;
}
